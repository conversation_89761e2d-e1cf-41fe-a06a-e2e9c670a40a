<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态滚动时长测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a1929;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .notification-area {
            display: flex;
            align-items: center;
            height: 35px;
            background: linear-gradient(135deg, #072040, #0a345e);
            padding: 0 15px;
            border-radius: 8px;
            border: 1px solid rgba(30, 192, 187, 0.3);
            position: relative;
            overflow: hidden;
            max-width: 800px;
            margin: 20px auto;
        }
        
        .notification-container {
            width: 580px;
            height: 100%;
            overflow: hidden;
            position: relative;
        }
        
        .alert {
            display: flex;
            align-items: center;
            height: 100%;
            gap: 10px;
        }
        
        .alert i {
            font-size: 16px;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .alert-text-container {
            overflow: hidden;
            width: 100%;
        }
        
        .alert-text {
            white-space: nowrap;
            display: inline-block;
            padding: 4px 0;
            transition: all 0.3s ease;
        }
        
        .scrolling-text {
            display: inline-block;
            padding-right: 50px;
            animation: scroll-text var(--scroll-duration, 15s) linear 1;
            animation-delay: var(--scroll-delay, 1s);
            animation-fill-mode: forwards;
        }
        
        @keyframes scroll-text {
            0%, 10% {
                transform: translateX(0);
            }
            90%, 100% {
                transform: translateX(calc(-100% - 50px));
            }
        }
        
        .bell {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-left: 15px;
            color: #ffa726;
        }
        
        .badge {
            background: #ff5722;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            min-width: 16px;
            text-align: center;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
        }
        
        button:hover {
            background: #1565c0;
        }
        
        .test-cases {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 20px auto;
            max-width: 800px;
        }
        
        .test-case {
            background: rgba(255,255,255,0.05);
            padding: 15px;
            border-radius: 5px;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .test-case h4 {
            margin-top: 0;
            color: #81c784;
        }
        
        .duration-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 20px auto;
            max-width: 800px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .duration-info h3 {
            margin-top: 0;
            color: #4fc3f7;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
            color: #aaa;
        }
    </style>
</head>
<body>
    <h1>动态滚动时长测试</h1>
    
    <div class="duration-info">
        <h3>🎯 动态时长计算规则</h3>
        <p><strong>计算公式：</strong> 总时长 = 延迟时间(2秒) + 滚动时间 + 缓冲时间(1秒)</p>
        <p><strong>滚动速度：</strong> 150像素/秒（较慢，便于阅读）</p>
        <p><strong>时长范围：</strong> 最少10秒，最多45秒</p>
        <p><strong>短文本：</strong> 不需要滚动时显示3秒</p>
    </div>
    
    <div class="notification-area">
        <div class="notification-container">
            <div class="alert">
                <i class="fas fa-exclamation-circle" style="color: #ff5a5a;" id="alertIcon">🔴</i>
                <div class="alert-text-container">
                    <div class="alert-text" id="alertText">
                        <span id="alertSpan">点击下方按钮测试不同长度的文本</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="bell">
            🔔
            <span class="badge" id="badgeCount">1</span>
        </div>
    </div>
    
    <div class="controls">
        <button onclick="testText(0)">短文本</button>
        <button onclick="testText(1)">中等文本</button>
        <button onclick="testText(2)">长文本</button>
        <button onclick="testText(3)">超长文本</button>
        <button onclick="stopAnimation()">停止</button>
    </div>
    
    <div class="status">
        <p>📊 当前状态：<span id="currentStatus">等待测试</span></p>
        <p>⏱️ 计算时长：<span id="calculatedDuration">-</span></p>
        <p>🎬 动画进度：<span id="animationProgress">-</span></p>
    </div>
    
    <div class="test-cases">
        <div class="test-case">
            <h4>📝 测试文本示例</h4>
            <p><strong>短文本：</strong> "兴通66 - 剩余时长超过48小时" (约20字)</p>
            <p><strong>中等文本：</strong> "兴通739 - 锚泊时长达到50小时，请及时关注船舶动态" (约30字)</p>
            <p><strong>长文本：</strong> "兴通17 - 锚泊时长超过72小时需要处理，请联系相关负责人进行紧急处理" (约40字)</p>
            <p><strong>超长文本：</strong> "兴通888 - 船舶在港口锚泊时长已达到96小时，严重超过规定时间，请立即联系港口管理部门和船舶负责人进行紧急处理，确保船舶安全和港口秩序" (约70字)</p>
        </div>
    </div>
    
    <script>
        const testTexts = [
            "兴通66 - 剩余时长超过48小时",
            "兴通739 - 锚泊时长达到50小时，请及时关注船舶动态",
            "兴通17 - 锚泊时长超过72小时需要处理，请联系相关负责人进行紧急处理",
            "兴通888 - 船舶在港口锚泊时长已达到96小时，严重超过规定时间，请立即联系港口管理部门和船舶负责人进行紧急处理，确保船舶安全和港口秩序"
        ];
        
        let currentAnimation = null;
        let startTime = null;
        
        function calculateTextWidth(text) {
            const tempSpan = document.createElement('span');
            tempSpan.style.visibility = 'hidden';
            tempSpan.style.position = 'absolute';
            tempSpan.style.whiteSpace = 'nowrap';
            tempSpan.style.font = '14px Arial, sans-serif';
            tempSpan.textContent = text;
            document.body.appendChild(tempSpan);
            const width = tempSpan.offsetWidth;
            document.body.removeChild(tempSpan);
            return width;
        }
        
        function calculateScrollDuration(text) {
            const textWidth = calculateTextWidth(text);
            const containerWidth = 580 - 50; // 容器宽度减去图标和边距
            
            if (textWidth <= containerWidth) {
                return 3000; // 不需要滚动
            }
            
            // 计算滚动距离和时长
            const scrollDistance = textWidth + 50;
            const readingSpeed = 150; // 每秒150像素
            const scrollTime = Math.ceil(scrollDistance / readingSpeed) * 1000;
            
            const delayTime = 2000;
            const bufferTime = 1000;
            const totalTime = delayTime + scrollTime + bufferTime;
            
            return Math.max(10000, Math.min(45000, totalTime));
        }
        
        function setScrollAnimationDuration(totalDuration) {
            const delayTime = 2000;
            const animationTime = Math.max(5000, totalDuration - delayTime - 1000);
            
            const alertText = document.getElementById('alertText');
            alertText.style.setProperty('--scroll-duration', `${animationTime}ms`);
            alertText.style.setProperty('--scroll-delay', `${delayTime}ms`);
            
            return { delayTime, animationTime, totalDuration };
        }
        
        function isTextOverflow(text) {
            const textWidth = calculateTextWidth(text);
            const containerWidth = 530; // 实际可用宽度
            return textWidth > containerWidth;
        }
        
        function testText(index) {
            stopAnimation();
            
            const text = testTexts[index];
            const textLength = text.length;
            
            // 更新显示
            document.getElementById('alertSpan').textContent = text;
            document.getElementById('currentStatus').textContent = `测试文本 ${index + 1} (${textLength}字)`;
            
            // 计算时长
            const duration = calculateScrollDuration(text);
            const needsScroll = isTextOverflow(text);
            
            document.getElementById('calculatedDuration').textContent = 
                needsScroll ? `${(duration/1000).toFixed(1)}秒 (需要滚动)` : `${(duration/1000).toFixed(1)}秒 (无需滚动)`;
            
            if (needsScroll) {
                // 设置动态时长并开始滚动
                const timing = setScrollAnimationDuration(duration);
                
                const alertText = document.getElementById('alertText');
                alertText.classList.remove('scrolling-text');
                alertText.offsetHeight; // 触发重排
                alertText.classList.add('scrolling-text');
                
                startTime = Date.now();
                updateProgress(duration);
                
                console.log(`文本: "${text.substring(0, 20)}..."`, {
                    字符数: textLength,
                    文本宽度: calculateTextWidth(text) + 'px',
                    延迟时间: timing.delayTime + 'ms',
                    动画时间: timing.animationTime + 'ms',
                    总时长: timing.totalDuration + 'ms'
                });
            } else {
                document.getElementById('animationProgress').textContent = '文本较短，无需滚动';
            }
        }
        
        function updateProgress(totalDuration) {
            if (!startTime) return;
            
            const elapsed = Date.now() - startTime;
            const percentage = Math.min((elapsed / totalDuration) * 100, 100);
            
            let status = '';
            if (elapsed < 2000) {
                status = `准备阶段 (${Math.ceil((2000 - elapsed) / 1000)}秒后开始)`;
            } else if (elapsed < totalDuration) {
                status = `滚动中 (${Math.floor(percentage)}% 完成)`;
            } else {
                status = '滚动完成';
            }
            
            document.getElementById('animationProgress').textContent = status;
            
            if (elapsed < totalDuration) {
                currentAnimation = setTimeout(() => updateProgress(totalDuration), 100);
            }
        }
        
        function stopAnimation() {
            if (currentAnimation) {
                clearTimeout(currentAnimation);
                currentAnimation = null;
            }
            startTime = null;
            
            const alertText = document.getElementById('alertText');
            alertText.classList.remove('scrolling-text');
            
            document.getElementById('animationProgress').textContent = '已停止';
        }
        
        // 初始显示
        document.getElementById('alertSpan').textContent = '点击下方按钮测试不同长度的文本滚动效果';
    </script>
</body>
</html>
